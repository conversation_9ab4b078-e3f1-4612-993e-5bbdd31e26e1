<!-- eslint-disable vue/no-multiple-template-root -->
<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="teacher-card">
    <div class="desktop-only">
      <nuxt-link :to="link" class="card-link"></nuxt-link>
      <div class="teacher-card-top top-desktop">
        <div class="teacher-card-top-inner">
          <div class="teacher-card-avatar">
            <l-avatar
              class="teacher-card-avatar"
              :avatars="teacher"
              :avatars-resized="teacher.avatarsResized"
              :languages-taught="[]"
              size="md"
              :eager="false"
            ></l-avatar>
          </div>

          <div class="teacher-card-top-content">
            <div class="teacher-card-header">
              <div class="teacher-card-name-rating">
                <div class="teacher-card-name">
                  {{ name }}
                </div>
                <div class="teacher-card-rating">
                  <template v-if="teacher.averageRatings === 0">
                    <div class="new-verified-teacher">
                      <div class="new-verified-teacher-icon">
                        <svg width="612" height="612" viewBox="0 0 612 612">
                          <use
                            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#verified-user`"
                          ></use>
                        </svg>
                      </div>
                      <span>{{ $t('new_verified_teacher') }}</span>
                    </div>
                  </template>
                  <template v-else>
                    <star-rating
                      :value="teacher.averageRatings"
                      medium
                    ></star-rating>
                    <div class="review">
                      ({{ $tc('review', teacher.countFeedbacks) }})
                    </div>
                  </template>
                </div>
                <div v-if="hasVideo" class="teacher-card-watch-video">
                  <v-btn
                    text
                    small
                    color="primary"
                    class="watch-video-btn"
                    @click.stop="watchVideo"
                  >
                    {{ $t('watch_video') }}
                    <v-icon left size="24">{{ mdiYoutube }}</v-icon>
                  </v-btn>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="teacher-card-center">
          <div
            class="teacher-card-description"
            v-html="formatContentWithHtml(teacher.description)"
          ></div>
        </div>
        <div class="teacher-card-price">
          {{ $t('from') }}
          <span
            >{{ currentCurrencySymbol
            }}{{ getPrice(teacher.pricePerHourOfLesson) }}/</span
          >hr
        </div>
        <template v-if="teacher.acceptNewStudents && teacher.freeSlots">
          <template v-if="teacher.bookLesson.freeTrial">
            <v-btn :to="link" small color="success">
              {{ $t('free_trial_lesson') }}
            </v-btn>
          </template>
          <template v-else>
            <v-btn
              v-if="teacher.bookLesson.price"
              :to="link"
              small
              color="orange"
            >
              {{ $t('trial') }}:&#160; {{ currentCurrencySymbol
              }}{{ getPrice(teacher.bookLesson.price) }}
            </v-btn>
          </template>
        </template>
        <template v-else>
          <v-btn :to="link" small color="greyDark">
            {{ $t('full_schedule') }}
          </v-btn>
        </template>
      </div>

      <div class="teacher-card-bottom">
        <div class="teacher-card-languages">
          <div class="language-flags">
            <div
              v-for="language in teacher.languagesTaught.slice(0, 2)"
              :key="language.isoCode"
              class="flag-item"
            >
              <v-img
                :src="require(`~/assets/images/flags/${language.isoCode}.svg`)"
                contain
              ></v-img>
            </div>
          </div>
          <div class="languages-text">
            <div class="teach-languages">
              {{ $t('i_teach') }}
              <strong>{{ getLanguagesString(teacher.languagesTaught) }}</strong>
            </div>
            <div
              v-if="getOtherLanguagesSpoken().length"
              class="speak-languages"
            >
              {{ $t('i_also_speak') }}&#160;
              {{ getOtherLanguagesSpoken().join(', ') }}
            </div>
          </div>
        </div>
        <div
          v-if="teacher.specialities.length"
          class="teacher-card-specialities-section"
        >
          <div class="specialities-label">{{ $t('top_specialities') }}:</div>
          <div class="teacher-card-specialities">
            <div
              v-for="(specialization, index) in teacher.specialities.slice(
                0,
                3
              )"
              :key="index"
              class="speciality-pill"
            >
              <span>{{
                getTranslatedSpecialityName(specialization.speciality)
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mobile-only">
      <nuxt-link :to="link" class="card-link"></nuxt-link>
      <div class="teacher-card-top">
        <div class="teacher-card-top-inner">
          <div class="teacher-card-avatar">
            <l-avatar
              class="teacher-card-avatar"
              :avatars="teacher"
              :avatars-resized="teacher.avatarsResized"
              :languages-taught="[]"
              size="lg"
              :eager="false"
            ></l-avatar>
          </div>

          <div class="teacher-card-top-content">
            <div class="teacher-card-header">
              <div class="teacher-card-name-rating">
                <div class="teacher-card-name">
                  {{ name }}
                </div>
                <div class="teacher-card-rating">
                  <template v-if="teacher.averageRatings === 0">
                    <div class="new-verified-teacher">
                      <div class="new-verified-teacher-icon">
                        <svg width="612" height="612" viewBox="0 0 612 612">
                          <use
                            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#verified-user`"
                          ></use>
                        </svg>
                      </div>
                      <span>{{ $t('new_verified_teacher') }}</span>
                    </div>
                  </template>
                  <template v-else>
                    <star-rating :value="teacher.averageRatings"></star-rating>
                    <div class="review">
                      ({{ $tc('review', teacher.countFeedbacks) }})
                    </div>
                  </template>
                </div>
                <div class="teacher-card-languages">
                  <div class="language-flags">
                    <div
                      v-for="language in teacher.languagesTaught.slice(0, 2)"
                      :key="language.isoCode"
                      class="flag-item"
                    >
                      <v-img
                        :src="
                          require(`~/assets/images/flags/${language.isoCode}.svg`)
                        "
                        contain
                      ></v-img>
                    </div>
                  </div>
                  <div class="languages-text">
                    <div class="teach-languages">
                      {{ $t('i_teach') }}
                      <strong>{{
                        getLanguagesString(teacher.languagesTaught)
                      }}</strong>
                    </div>
                    <div
                      v-if="getOtherLanguagesSpoken().length"
                      class="speak-languages"
                    >
                      {{ $t('i_also_speak') }}
                      {{ getOtherLanguagesSpoken().join(', ') }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="teacher-card-bottom">
          <div
            v-if="teacher.specialities.length"
            class="teacher-card-specialities-section"
          ></div>
        </div>
        <div class="teacher-card-center">
          <div
            class="teacher-card-description"
            v-html="formatContentWithHtml(teacher.description)"
          ></div>
        </div>
        <div class="teacher-card-specialities">
          <div class="specialities-label">
            {{ $t('top_specialities') }}:
            <div
              v-for="(specialization, index) in teacher.specialities.slice(
                0,
                3
              )"
              :key="index"
              class="speciality-pill"
            >
              {{ getTranslatedSpecialityName(specialization.speciality) }}
            </div>
          </div>
        </div>
        <div class="price-mobile">
          <div>
            <template v-if="teacher.acceptNewStudents && teacher.freeSlots">
              <template v-if="teacher.bookLesson.freeTrial">
                <v-btn :to="link" small color="success">
                  {{ $t('free_trial_lesson') }}
                </v-btn>
              </template>
              <template v-else>
                <v-btn
                  v-if="teacher.bookLesson.price"
                  :to="link"
                  small
                  color="orange"
                >
                  {{ $t('trial') }}:&#160; {{ currentCurrencySymbol
                  }}{{ getPrice(teacher.bookLesson.price) }}
                </v-btn>
              </template>
            </template>
            <template v-else>
              <v-btn :to="link" small color="greyDark">
                {{ $t('full_schedule') }}
              </v-btn>
            </template>
          </div>
          <div class="teacher-card-price">
            {{ $t('from') }}
            <span
              >{{ currentCurrencySymbol
              }}{{ getPrice(teacher.pricePerHourOfLesson) }}/</span
            >hr
          </div>
        </div>
      </div>
    </div>

    <!-- Video Modal -->
    <v-dialog
      v-model="showVideoModal"
      max-width="800px"
      content-class="video-modal"
      @click:outside="closeVideoModal"
    >
      <div class="video-container">
        <iframe
          v-if="showVideoModal && teacher.videoLink"
          :src="getYouTubeEmbedUrl(teacher.videoLink)"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen
          width="100%"
          height="450"
        ></iframe>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import { mdiYoutube } from '@mdi/js'
import { getPrice } from '~/helpers'
import { translateLanguageName } from '~/constants/languageTranslations'

import LAvatar from '~/components/LAvatar'
import StarRating from '~/components/StarRating'

export default {
  name: 'TeacherCard',
  components: { LAvatar, StarRating },
  filters: {
    specialitiesStr(arr) {
      let str = ''

      for (let i = 0; i < 3; i++) {
        str += arr[i]

        if (i < 3 - 1) {
          str += ', '
        }
      }

      return str
    },
  },
  props: {
    teacher: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      getPrice,
      mdiYoutube,
      showVideoModal: false,
    }
  },
  computed: {
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol']
    },
    link() {
      return this.teacher.profileLink
    },
    name() {
      // Split the string into words by spaces and set first word as array element
      return [
        `${this.teacher.firstName?.split(' ')[0]?.toLowerCase()}`,
        `${this.teacher.lastName?.split(' ')[0]?.toLowerCase()}`,
      ]
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter of each word
        .join(' ') // Join the words back together with spaces
    },
    hasVideo() {
      return this.teacher.videoLink && this.teacher.videoLink.trim() !== ''
    },
    isI18nReady() {
      return (
        this.$t &&
        typeof this.$t === 'function' &&
        this.$i18n &&
        this.$i18n.locale
      )
    },
  },
  methods: {
    getTranslatedSpecialityName(speciality) {
      if (
        !speciality ||
        !speciality.translations ||
        !this.$i18n ||
        !this.$i18n.locale
      ) {
        return speciality?.name || ''
      }
      const currentLocale = this.$i18n.locale
      const translation = speciality.translations.find(
        (t) => t.locale === currentLocale && t.field === 'name'
      )
      return translation ? translation.content : speciality.name
    },
    formatContentWithHtml(content) {
      if (!content) return null

      const contentArray = content.split(/\n/)
      let output = ''
      let isListStarted = false

      for (let i = 0; i < contentArray.length; i++) {
        const contentLine = contentArray[i]

        if (!contentLine.trim().length) {
          if (isListStarted) {
            isListStarted = false
            output += '</ul>'
          }
          continue
        }

        if (contentLine.substr(0, 1) !== '*') {
          if (isListStarted) {
            isListStarted = false
            output += '</ul>'
          }

          output += contentLine + ' '
          continue
        }

        if (!isListStarted && contentLine.substr(0, 1) === '*') {
          output += '<ul>'
          isListStarted = true
        }

        output += '<li>' + contentLine.substr(1) + '</li>'
      }

      if (isListStarted) {
        output += '</ul>'
      }

      return output
    },
    getLanguagesString(languages) {
      if (!languages || languages.length === 0) return ''

      // Translate language names using the shared translation function
      // Check if i18n is ready before translating
      const translatedLanguages = languages.map((lang) => {
        if (this.isI18nReady) {
          return translateLanguageName(lang.name, this.$t, this.$i18n.locale)
        }
        return lang.name // Fallback to original name if i18n is not ready
      })

      if (translatedLanguages.length === 1) {
        return translatedLanguages[0]
      } else if (translatedLanguages.length === 2) {
        return `${translatedLanguages[0]} & ${translatedLanguages[1]}`
      } else {
        const firstTwo = translatedLanguages.slice(0, 2).join(' & ')
        const remaining = translatedLanguages.slice(2).join(', ')
        return `${firstTwo}, ${remaining}`
      }
    },
    watchVideo() {
      if (this.teacher.videoLink) {
        // Open video in modal popup
        this.showVideoModal = true
      }
    },
    closeVideoModal() {
      this.showVideoModal = false
    },
    getYouTubeEmbedUrl(url) {
      // Convert YouTube watch URL to embed URL
      if (url.includes('youtube.com/watch?v=')) {
        const videoId = url.split('v=')[1].split('&')[0]
        return `https://www.youtube.com/embed/${videoId}?autoplay=1`
      } else if (url.includes('youtu.be/')) {
        const videoId = url.split('youtu.be/')[1].split('?')[0]
        return `https://www.youtube.com/embed/${videoId}?autoplay=1`
      }
      return url
    },
    getOtherLanguagesSpoken() {
      // Check for otherLanguagesSpoken field (from teacher details API)
      if (
        this.teacher.otherLanguagesSpoken &&
        this.teacher.otherLanguagesSpoken.length
      ) {
        return this.teacher.otherLanguagesSpoken.map((lang) => {
          const languageName = lang.name || lang
          if (this.isI18nReady) {
            return translateLanguageName(
              languageName,
              this.$t,
              this.$i18n.locale,
              'speak' // Use 'speak' context for nominative case
            )
          }
          return languageName // Fallback to original name if i18n is not ready
        })
      }

      // Fallback to languagesSpoken field (if available in teacher listing API)
      if (this.teacher.languagesSpoken && this.teacher.languagesSpoken.length) {
        return this.teacher.languagesSpoken.map((lang) => {
          const languageName = lang.name || lang
          if (this.isI18nReady) {
            return translateLanguageName(
              languageName,
              this.$t,
              this.$i18n.locale,
              'speak' // Use 'speak' context for nominative case
            )
          }
          return languageName // Fallback to original name if i18n is not ready
        })
      }

      // Return empty array if no spoken languages data is available
      return []
    },
  },
}
</script>

<style scoped lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.teacher-card {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 20px;
  box-shadow: 0 8px 17px rgba(17, 46, 90, 0.1);
  border-radius: 20px;
  background-color: #fff;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 24px rgba(17, 46, 90, 0.15);
  }

  .top-desktop {
    max-width: 600px;
    min-width: 300px;

    // Better responsive behavior for tablet sizes
    @media screen and (min-width: 768px) and (max-width: 991px) {
      max-width: 100%;
      min-width: auto;
    }

    @media screen and (min-width: 992px) and (max-width: 1199px) {
      max-width: 500px;
      min-width: 200px;
    }

    @media screen and (min-width: 1200px) and (max-width: 1399px) {
      max-width: 550px;
      min-width: 300px;
    }

    @media screen and (min-width: 1400px) {
      max-width: 450px;
      min-width: 400px;
    }
  }
  .price-mobile {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
  }
  .mobile-only {
    display: none;
  }
  .desktop-only {
    display: flex;
  }

  .card-link {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    text-decoration: none;
    border-radius: 20px;
  }

  // Custom breakpoint for tablet sizes (767px - 860px) to show mobile view
  @media screen and (min-width: 767px) and (max-width: 860px) {
    .mobile-only {
      display: block;
    }
    .desktop-only {
      display: none;
    }

    // Remove margin between top and bottom sections
    .teacher-card-top {
      border-right: 0;
      padding-right: 0px;
    }

    .teacher-card-bottom {
      margin-right: 0;
    }

    // Apply mobile-specific styling for specialities
    .specialities-label {
      font-size: 14px;
      color: var(--v-success-base);
      font-weight: 900;
    }

    .speciality-pill {
      display: inline;
      margin-right: 5px;
      padding: 0;
      background: none;
      border: none;
      background: transparent !important;
      -webkit-text-fill-color: #2d2d2d !important;
      font-weight: 700;
      &::before {
        display: none;
      }
      &:not(:last-child)::after {
        content: ', ';
        color: #000;
        margin-left: -2px;
      }
    }

    .teacher-card-specialities {
      display: inline;
      gap: 0;
    }
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    max-width: 478px;

    .mobile-only {
      display: block;
    }
    .desktop-only {
      display: none;
    }

    .specialities-label {
      font-size: 16px;
      @media screen and (max-width: 767px) {
        color: var(--v-success-base);
        font-weight: 900;
        line-height: 20px;
        display: inline;
      }
    }
  }

  & > a {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 20px;
    z-index: 3;
  }

  &-top {
    display: flex;
    gap: 10px;
    flex-direction: column;
    border-right: 1px solid #8bc34a;
    width: 100%;
    padding-right: 10px;
    align-items: flex-start;
    justify-content: space-between;
    .v-btn {
      position: relative;
      z-index: 2;
    }
    @media screen and (max-width: 767px) {
      border-right: none;
    }
    &-inner {
      display: flex;
      flex-direction: row;
      gap: 15px;
    }
    &-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }
  &-center {
    height: 100%;
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;

    @media only screen and (max-width: $mac-13-and-down) {
      flex-direction: column;
      gap: 15px;
    }
  }

  &-name-rating {
    flex: 1;
    .teacher-card-rating {
      .score {
        span {
          color: #000 !important;
        }
      }
    }
  }

  &-languages {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
    margin-top: 5px;
    max-width: 300px;

    @media only screen and (max-width: $mac-13-and-down) {
      max-width: 100%;
    }
  }

  &-bottom {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    gap: 10px;
    width: 35%;
    margin-left: 10px;
    @media screen and (max-width: 767px) {
      width: 100%;
      margin-left: 0;
    }
    .teacher-card-languages {
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      gap: 10px;
    }
  }

  &-name {
    font-size: 20px;
    font-weight: 700;
    line-height: 1.4;
    @media only screen and (max-width: $mac-13-and-down) {
      font-size: 20px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 18px;
    }
  }

  .language-flags {
    display: flex;
    flex-direction: row;
    gap: 8px;
    min-width: 50px;

    .flag-item {
      width: 42px;
      height: 32px;
      border: 1px solid #000000;
      border-radius: 8px;
      overflow: hidden;
      filter: drop-shadow(2px 2px 12px rgba(146, 138, 138, 0.2));

      @media only screen and (max-width: $mac-13-and-down) {
        width: 37px;
        height: 28px;
      }

      .v-image {
        width: 100% !important;
        height: 100% !important;
      }
    }
  }

  .languages-text {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;

    .teach-languages {
      strong {
        font-weight: 600;
      }
    }

    .speak-languages {
      color: rgba(45, 45, 45, 0.7);
      font-size: 13px;
    }
  }

  .watch-video-btn {
    padding: 0 !important;
    min-width: auto !important;
    height: auto !important;
    font-size: 14px !important;
    text-transform: none !important;
    color: #000000 !important;
    font-weight: 700 !important;
    font-style: bold !important;
    .v-icon {
      font-size: 18px !important;
      color: #d32f2f !important;
      margin-left: 4px !important;
    }

    &:hover {
      color: var(--v-primary-base) !important;
    }
  }

  &-rating {
    display: flex;
    align-items: center;
    gap: 3px;

    .new-verified-teacher {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      font-weight: 500;

      &-icon {
        width: 16px;
        height: 16px;

        svg {
          width: 100%;
          height: 100%;
        }
      }
    }

    .review {
      color: #2d2d2d;
      font-size: 14px;
      font-weight: 500;
      line-height: 18px;
      letter-spacing: 0.1px;
    }
  }

  &-description {
    font-weight: 400;
    font-size: 16px;
    line-height: 1.4;
    color: #000000;
    margin-bottom: 10px;

    @media only screen and (max-width: $xxs-and-down) {
      font-size: 16px;
    }
  }

  &-specialities-section {
    .specialities-label {
      font-size: 14px;
      font-weight: 700;
      color: var(--v-success-base);
      background: linear-gradient(
        -75deg,
        var(--v-success-base),
        var(--v-primary-base)
      );
      background: -webkit-linear-gradient(
        -75deg,
        var(--v-success-base),
        var(--v-primary-base)
      );
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;

      @media screen and (max-width: 767px) {
        display: inline;
        margin-right: 5px;
      }
    }
  }

  &-specialities {
    display: block;
    @media screen and (max-width: 767px) {
      display: inline;
      gap: 0;
    }

    .speciality-pill {
      position: relative;
      padding: 1.5px;
      background: linear-gradient(126.15deg, #80b622 0%, #3c87f8 102.93%);
      border-radius: 20px;
      width: fit-content;
      margin-top: 5px;
      transition: all 0.2s ease;

      &::before {
        content: '';
        position: absolute;
        top: 1.5px;
        left: 1.5px;
        right: 1.5px;
        bottom: 1.5px;
        background: #fff;
        border-radius: 18.5px;
        z-index: 1;
      }

      span {
        position: relative;
        z-index: 2;
        display: block;
        padding: 6px 12px;
        font-size: 13px;
        font-weight: 700;
        line-height: 1.2;
        color: var(--v-success-base);
        background: linear-gradient(
          -75deg,
          var(--v-success-base),
          var(--v-primary-base)
        );
        background: -webkit-linear-gradient(
          -75deg,
          var(--v-success-base),
          var(--v-primary-base)
        );
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      @media screen and (max-width: 767px) {
        display: inline;
        padding: 0;
        background: transparent;
        border-radius: 0;
        width: auto;
        margin-top: 0;

        &::before {
          display: none;
        }
        font-weight: 700;
        padding: 0;
        font-size: 14px;
        background: transparent !important;
        -webkit-text-fill-color: #2d2d2d !important;

        &:not(:last-child)::after {
          content: ', ';
          color: #000;
          margin-left: -2px;
        }
      }

      &:hover {
        background-color: #8bc34a;
        color: white;

        @media screen and (max-width: 767px) {
          background-color: transparent;
          color: #000;
        }
      }
    }
  }

  &-price {
    padding-right: 5px;
    font-size: 16px;
    font-style: italic;

    span {
      font-size: 16px;
      font-style: italic;
    }
  }
}
</style>

<style lang="scss">
@import './assets/styles/vars';

.teacher-card-avatar {
  position: relative;
  width: 100px;
  height: 100px;
  flex-shrink: 0;

  .l-avatar .v-avatar .v-skeleton-loader > div {
    border-radius: 20%;
  }

  @media only screen and (max-width: $xxs-and-down) {
    width: 100px;
    height: 100px;
  }

  .v-avatar {
    border-radius: 20% !important;
  }
}

// Video Modal Styles
.video-modal {
  background: rgba(0, 0, 0, 0.8) !important;
  backdrop-filter: blur(4px);

  .video-container {
    background: #000;
    border-radius: 8px;
    overflow: hidden;

    iframe {
      display: block;
      width: 100%;
      height: 450px;

      @media screen and (max-width: 767px) {
        height: 250px;
      }
    }
  }
}
</style>
