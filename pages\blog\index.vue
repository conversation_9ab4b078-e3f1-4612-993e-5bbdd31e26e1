<template>
  <div id="dib-posts"></div>
</template>

<script>
export default {
  head() {
    return {
      title:
        this.$i18n.locale === 'pl'
          ? 'Langu | Blog: Artykuły i zasoby do efektywnej nauki języków online'
          : 'Langu | Blog: Articles and Resources for Effective Online Language Learning',
    }
  },
  mounted() {
    this.loadDropInBlogScript()
  },
  methods: {
    loadDropInBlogScript() {
      // Check if the script is already loaded and main function exists
      if (typeof window.main === 'function') {
        window.main()
        return
      }

      // Check if script is already being loaded or exists
      const existingScript = document.querySelector(
        'script[src="https://io.dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445.js"]'
      )

      if (existingScript) {
        // Script exists, wait for it to load and call main
        if (existingScript.onload) {
          // Script is still loading, add our callback to existing onload
          const originalOnload = existingScript.onload
          existingScript.onload = () => {
            originalOnload()
            if (typeof window.main === 'function') {
              window.main()
            }
          }
        } else if (typeof window.main === 'function') {
          // Script is already loaded, call main directly
          window.main()
        }
        return
      }

      // Create and load the script
      const script = document.createElement('script')
      script.src =
        'https://io.dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445.js'
      script.async = true
      script.defer = true
      script.onload = () => {
        if (typeof window.main === 'function') {
          window.main()
        }
      }
      script.onerror = () => {
        // eslint-disable-next-line no-console
        console.error('Failed to load DropInBlog script')
      }
      document.head.appendChild(script)
    },
  },
}
</script>

<style lang="scss">
@import '~assets/styles/faq-page.scss';
</style>
